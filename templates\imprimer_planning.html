<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Planning des Rendez-vous</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 10pt; }
            .table { font-size: 9pt; }
            .card { border: 1px solid #000 !important; }
            .card-header { background-color: #f8f9fa !important; color: #000 !important; }
            .page-break { page-break-before: always; }
        }
        
        @media screen {
            body { background-color: #f8f9fa; padding: 20px; }
        }
        
        .logo-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }
        
        .filters-summary {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Boutons d'action (masqués à l'impression) -->
        <div class="no-print mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <a href="{{ url_for('liste_rendez_vous') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour à la liste
                </a>
                
                <!-- Formulaire de filtres -->
                <form method="GET" class="d-flex gap-2 align-items-center">
                    <input type="date" name="date_debut" value="{{ date_debut or '' }}" class="form-control form-control-sm" placeholder="Date début">
                    <input type="date" name="date_fin" value="{{ date_fin or '' }}" class="form-control form-control-sm" placeholder="Date fin">
                    <select name="kine_id" class="form-select form-select-sm">
                        <option value="">Tous les kinés</option>
                        {% for kine in kinesitherapeutes %}
                        <option value="{{ kine.id }}" {% if kine_id == kine.id|string %}selected{% endif %}>
                            {{ kine.prenom }} {{ kine.nom }}
                        </option>
                        {% endfor %}
                    </select>
                    <button type="submit" class="btn btn-outline-primary btn-sm">Filtrer</button>
                </form>
                
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="bi bi-printer"></i> Imprimer
                </button>
            </div>
        </div>

        <!-- Contenu à imprimer -->
        <div class="card">
            <!-- En-tête -->
            <div class="logo-header">
                <h2 class="text-primary mb-1">Cabinet de Kinésithérapie</h2>
                <p class="mb-0">Planning des Rendez-vous</p>
            </div>

            <div class="card-body">
                <!-- Résumé des filtres -->
                {% if date_debut or date_fin or kine_id %}
                <div class="filters-summary">
                    <h6><i class="bi bi-funnel"></i> Filtres appliqués :</h6>
                    <ul class="mb-0">
                        {% if date_debut %}
                        <li>Date de début : {{ date_debut }}</li>
                        {% endif %}
                        {% if date_fin %}
                        <li>Date de fin : {{ date_fin }}</li>
                        {% endif %}
                        {% if kine_id %}
                        {% for kine in kinesitherapeutes %}
                        {% if kine.id == kine_id|int %}
                        <li>Kinésithérapeute : {{ kine.prenom }} {{ kine.nom }}</li>
                        {% endif %}
                        {% endfor %}
                        {% endif %}
                    </ul>
                </div>
                {% endif %}

                <!-- Statistiques -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5>{{ rendez_vous|length }}</h5>
                                <small>Rendez-vous total</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5>{{ rendez_vous|selectattr('statut', 'equalto', 'confirmé')|list|length }}</h5>
                                <small>Confirmés</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h5>{{ rendez_vous|selectattr('statut', 'equalto', 'terminé')|list|length }}</h5>
                                <small>Terminés</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h5>{{ rendez_vous|selectattr('statut', 'equalto', 'annulé')|list|length }}</h5>
                                <small>Annulés</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tableau des rendez-vous -->
                {% if rendez_vous %}
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>Date</th>
                                <th>Heure</th>
                                <th>Patient</th>
                                <th>Téléphone</th>
                                <th>Kinésithérapeute</th>
                                <th>Durée</th>
                                <th>Motif</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set current_date = '' %}
                            {% for rdv in rendez_vous %}
                            {% if rdv.date_rdv.strftime('%Y-%m-%d') != current_date %}
                            {% set current_date = rdv.date_rdv.strftime('%Y-%m-%d') %}
                            <tr class="table-info">
                                <td colspan="8" class="fw-bold">
                                    <i class="bi bi-calendar-date"></i> {{ rdv.date_rdv.strftime('%A %d %B %Y') }}
                                </td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td>{{ rdv.date_rdv.strftime('%d/%m') }}</td>
                                <td><strong>{{ rdv.heure_rdv.strftime('%H:%M') }}</strong></td>
                                <td>{{ rdv.patient.prenom }} {{ rdv.patient.nom }}</td>
                                <td>{{ rdv.patient.telephone }}</td>
                                <td>{{ rdv.kinesitherapeute.prenom }} {{ rdv.kinesitherapeute.nom }}</td>
                                <td>{{ rdv.duree }}min</td>
                                <td>
                                    {% if rdv.motif %}
                                    {{ rdv.motif[:30] }}{% if rdv.motif|length > 30 %}...{% endif %}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{% if rdv.statut == 'confirmé' %}success{% elif rdv.statut == 'annulé' %}danger{% elif rdv.statut == 'terminé' %}warning{% else %}primary{% endif %}">
                                        {{ rdv.statut }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-calendar-x fs-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">Aucun rendez-vous trouvé</h5>
                    <p class="text-muted">Aucun rendez-vous ne correspond aux critères sélectionnés.</p>
                </div>
                {% endif %}

                <!-- Date d'impression -->
                <div class="text-center mt-4" style="font-size: 0.9em; color: #6c757d;">
                    <small>Planning imprimé le <span id="print-date"></span></small>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Afficher la date d'impression
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('fr-FR') + ' à ' + now.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
            document.getElementById('print-date').textContent = dateStr;
        });
    </script>
</body>
</html>
