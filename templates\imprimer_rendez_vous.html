<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rendez-vous - {{ rendez_vous.patient.prenom }} {{ rendez_vous.patient.nom }}</title>
    
    <!-- Bootstrap CSS pour l'impression -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12pt; }
            .card { border: 1px solid #000 !important; }
            .card-header { background-color: #f8f9fa !important; color: #000 !important; }
        }
        
        @media screen {
            body { background-color: #f8f9fa; padding: 20px; }
        }
        
        .logo-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }
        
        .info-section {
            margin-bottom: 20px;
        }
        
        .signature-section {
            margin-top: 50px;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        
        .print-date {
            font-size: 0.9em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Boutons d'action (masqués à l'impression) -->
        <div class="no-print mb-3">
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('liste_rendez_vous') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour à la liste
                </a>
                <div class="btn-group">
                    <a href="{{ url_for('recu_a4', rdv_id=rendez_vous.id) }}"
                       class="btn btn-success"
                       target="_blank">
                        <i class="bi bi-receipt"></i> Reçu A4
                    </a>
                    <button onclick="window.print()" class="btn btn-primary">
                        <i class="bi bi-printer"></i> Imprimer
                    </button>
                </div>
            </div>
        </div>

        <!-- Contenu à imprimer -->
        <div class="card">
            <!-- En-tête -->
            <div class="logo-header">
                <h2 class="text-primary mb-1">Cabinet de Kinésithérapie</h2>
                <p class="mb-0">Confirmation de Rendez-vous</p>
            </div>

            <div class="card-body">
                <!-- Informations du rendez-vous -->
                <div class="row info-section">
                    <div class="col-12">
                        <h4 class="text-center mb-4 text-primary">RENDEZ-VOUS</h4>
                    </div>
                </div>

                <div class="row info-section">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-person"></i> Informations Patient</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Nom :</strong> {{ rendez_vous.patient.nom }}</p>
                                <p><strong>Prénom :</strong> {{ rendez_vous.patient.prenom }}</p>
                                <p><strong>Téléphone :</strong> {{ rendez_vous.patient.telephone }}</p>
                                <p><strong>Email :</strong> {{ rendez_vous.patient.email }}</p>
                                <p class="mb-0"><strong>Date de naissance :</strong> {{ rendez_vous.patient.date_naissance.strftime('%d/%m/%Y') }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-calendar-check"></i> Détails du Rendez-vous</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Date :</strong> {{ rendez_vous.date_rdv.strftime('%A %d %B %Y') }}</p>
                                <p><strong>Heure :</strong> {{ rendez_vous.heure_rdv.strftime('%H:%M') }}</p>
                                <p><strong>Durée :</strong> {{ rendez_vous.duree }} minutes</p>
                                <p><strong>Statut :</strong> 
                                    <span class="badge bg-{% if rendez_vous.statut == 'confirmé' %}success{% elif rendez_vous.statut == 'annulé' %}danger{% else %}primary{% endif %}">
                                        {{ rendez_vous.statut|title }}
                                    </span>
                                </p>
                                <p class="mb-0"><strong>Référence :</strong> RDV-{{ rendez_vous.id }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations du kinésithérapeute -->
                <div class="row info-section">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-person-badge"></i> Kinésithérapeute</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Nom :</strong> {{ rendez_vous.kinesitherapeute.prenom }} {{ rendez_vous.kinesitherapeute.nom }}</p>
                                        <p class="mb-0"><strong>Spécialité :</strong> {{ rendez_vous.kinesitherapeute.specialite }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        {% if rendez_vous.kinesitherapeute.telephone %}
                                        <p><strong>Téléphone :</strong> {{ rendez_vous.kinesitherapeute.telephone }}</p>
                                        {% endif %}
                                        {% if rendez_vous.kinesitherapeute.email %}
                                        <p class="mb-0"><strong>Email :</strong> {{ rendez_vous.kinesitherapeute.email }}</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Motif de consultation -->
                {% if rendez_vous.motif %}
                <div class="row info-section">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-file-text"></i> Motif de consultation</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">{{ rendez_vous.motif }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Notes -->
                {% if rendez_vous.notes %}
                <div class="row info-section">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-sticky"></i> Notes</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">{{ rendez_vous.notes }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Instructions importantes -->
                <div class="row info-section">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> Instructions importantes :</h6>
                            <ul class="mb-0">
                                <li>Merci d'arriver 10 minutes avant votre rendez-vous</li>
                                <li>Apportez votre ordonnance et vos examens médicaux</li>
                                <li>Portez une tenue confortable pour les exercices</li>
                                <li>En cas d'empêchement, merci de prévenir 24h à l'avance</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Section signature -->
                <div class="signature-section">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Signature du patient :</strong></p>
                            <div style="height: 60px; border-bottom: 1px solid #000; margin-top: 20px;"></div>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Signature du kinésithérapeute :</strong></p>
                            <div style="height: 60px; border-bottom: 1px solid #000; margin-top: 20px;"></div>
                        </div>
                    </div>
                </div>

                <!-- Date d'impression -->
                <div class="text-center print-date mt-4">
                    <small id="print-date-text">Document imprimé le ...</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <script>
        // Afficher la date d'impression
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('fr-FR') + ' à ' + now.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
            const printDateElement = document.getElementById('print-date-text');
            if (printDateElement) {
                printDateElement.textContent = 'Document imprimé le ' + dateStr;
            }
        });
    </script>
</body>
</html>
