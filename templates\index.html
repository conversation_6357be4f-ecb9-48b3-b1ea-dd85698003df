{% extends "base.html" %}

{% block title %}Accueil - Syst<PERSON> de Rendez-vous Kinésithérapie{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-light p-5 rounded-3 mb-4">
            <div class="container-fluid py-5">
                <h1 class="display-5 fw-bold text-primary">
                    <i class="bi bi-heart-pulse"></i> Bienvenue dans votre système de rendez-vous
                </h1>
                <p class="col-md-8 fs-4">
                    Gérez facilement vos rendez-vous de kinésithérapie. 
                    Ajoutez de nouveaux patients, planifiez des séances et suivez vos consultations.
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Nouveau Patient -->
    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-header text-center">
                <i class="bi bi-person-plus fs-1"></i>
            </div>
            <div class="card-body text-center">
                <h5 class="card-title">Nouveau Patient</h5>
                <p class="card-text">
                    Enregistrez un nouveau patient dans le système avec ses informations personnelles.
                </p>
                <a href="{{ url_for('nouveau_patient') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Ajouter un patient
                </a>
            </div>
        </div>
    </div>

    <!-- Nouveau Rendez-vous -->
    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-header text-center">
                <i class="bi bi-calendar-plus fs-1"></i>
            </div>
            <div class="card-body text-center">
                <h5 class="card-title">Nouveau Rendez-vous</h5>
                <p class="card-text">
                    Planifiez un nouveau rendez-vous pour un patient avec un kinésithérapeute.
                </p>
                <a href="{{ url_for('nouveau_rendez_vous') }}" class="btn btn-primary">
                    <i class="bi bi-calendar-plus"></i> Prendre RDV
                </a>
            </div>
        </div>
    </div>

    <!-- Liste des Rendez-vous -->
    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-header text-center">
                <i class="bi bi-calendar-check fs-1"></i>
            </div>
            <div class="card-body text-center">
                <h5 class="card-title">Rendez-vous</h5>
                <p class="card-text">
                    Consultez la liste de tous les rendez-vous programmés et leur statut.
                </p>
                <a href="{{ url_for('liste_rendez_vous') }}" class="btn btn-primary">
                    <i class="bi bi-list-check"></i> Voir les RDV
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mt-5">
    <div class="col-12">
        <h3 class="text-center mb-4">Aperçu rapide</h3>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-people fs-2"></i>
                <h4 class="card-title mt-2">Patients</h4>
                <p class="card-text fs-5">{{ patients_count if patients_count else 0 }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-calendar-check fs-2"></i>
                <h4 class="card-title mt-2">RDV Aujourd'hui</h4>
                <p class="card-text fs-5">{{ rdv_aujourd_hui if rdv_aujourd_hui else 0 }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="bi bi-calendar-week fs-2"></i>
                <h4 class="card-title mt-2">RDV Cette Semaine</h4>
                <p class="card-text fs-5">{{ rdv_semaine if rdv_semaine else 0 }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-person-badge fs-2"></i>
                <h4 class="card-title mt-2">Kinésithérapeutes</h4>
                <p class="card-text fs-5">{{ kines_count if kines_count else 0 }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
