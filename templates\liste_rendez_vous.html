{% extends "base.html" %}

{% block title %}Liste des Rendez-vous{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="bi bi-calendar-check"></i> Liste des Rendez-vous
                </h4>
                <a href="{{ url_for('nouveau_rendez_vous') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Nouveau RDV
                </a>
            </div>
            <div class="card-body">
                {% if rendez_vous %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Date</th>
                                <th>Heure</th>
                                <th>Patient</th>
                                <th>Kinésithérapeute</th>
                                <th><PERSON><PERSON><PERSON></th>
                                <th>Motif</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for rdv in rendez_vous %}
                            <tr>
                                <td>{{ rdv.date_rdv.strftime('%d/%m/%Y') }}</td>
                                <td>{{ rdv.heure_rdv.strftime('%H:%M') }}</td>
                                <td>{{ rdv.patient.prenom }} {{ rdv.patient.nom }}</td>
                                <td>{{ rdv.kinesitherapeute.prenom }} {{ rdv.kinesitherapeute.nom }}</td>
                                <td>{{ rdv.duree }} min</td>
                                <td>{{ rdv.motif[:50] }}{% if rdv.motif|length > 50 %}...{% endif %}</td>
                                <td>
                                    {% if rdv.statut == 'confirmé' %}
                                        <span class="badge bg-success">{{ rdv.statut }}</span>
                                    {% elif rdv.statut == 'annulé' %}
                                        <span class="badge bg-danger">{{ rdv.statut }}</span>
                                    {% else %}
                                        <span class="badge bg-primary">{{ rdv.statut }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-calendar-x fs-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">Aucun rendez-vous programmé</h5>
                    <p class="text-muted">Commencez par ajouter un nouveau rendez-vous.</p>
                    <a href="{{ url_for('nouveau_rendez_vous') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Premier rendez-vous
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
