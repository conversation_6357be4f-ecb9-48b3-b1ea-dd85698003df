from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date, time
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'votre-cle-secrete-ici'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///kinesitherapie.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# Modèles de base de données
class Patient(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    telephone = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    adresse = db.Column(db.Text)
    rendez_vous = db.relationship('RendezVous', backref='patient', lazy=True)

    def __repr__(self):
        return f'<Patient {self.prenom} {self.nom}>'

class Kinesitherapeute(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    specialite = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    rendez_vous = db.relationship('RendezVous', backref='kinesitherapeute', lazy=True)

    def __repr__(self):
        return f'<Kinesitherapeute {self.prenom} {self.nom}>'

class RendezVous(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patient.id'), nullable=False)
    kinesitherapeute_id = db.Column(db.Integer, db.ForeignKey('kinesitherapeute.id'), nullable=False)
    date_rdv = db.Column(db.Date, nullable=False)
    heure_rdv = db.Column(db.Time, nullable=False)
    duree = db.Column(db.Integer, default=60)  # durée en minutes
    motif = db.Column(db.Text)
    statut = db.Column(db.String(20), default='confirmé')  # confirmé, annulé, terminé
    notes = db.Column(db.Text)
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<RendezVous {self.date_rdv} {self.heure_rdv}>'

# Routes
@app.route('/')
def index():
    # Statistiques pour la page d'accueil
    patients_count = Patient.query.count()
    kines_count = Kinesitherapeute.query.count()

    # Rendez-vous d'aujourd'hui
    from datetime import date
    today = date.today()
    rdv_aujourd_hui = RendezVous.query.filter_by(date_rdv=today).count()

    # Rendez-vous de cette semaine
    from datetime import timedelta
    start_week = today - timedelta(days=today.weekday())
    end_week = start_week + timedelta(days=6)
    rdv_semaine = RendezVous.query.filter(
        RendezVous.date_rdv >= start_week,
        RendezVous.date_rdv <= end_week
    ).count()

    return render_template('index.html',
                         patients_count=patients_count,
                         kines_count=kines_count,
                         rdv_aujourd_hui=rdv_aujourd_hui,
                         rdv_semaine=rdv_semaine)

@app.route('/nouveau-patient')
def nouveau_patient():
    return render_template('nouveau_patient.html')

@app.route('/ajouter-patient', methods=['POST'])
def ajouter_patient():
    try:
        patient = Patient(
            nom=request.form['nom'],
            prenom=request.form['prenom'],
            telephone=request.form['telephone'],
            email=request.form['email'],
            date_naissance=datetime.strptime(request.form['date_naissance'], '%Y-%m-%d').date(),
            adresse=request.form.get('adresse', '')
        )
        db.session.add(patient)
        db.session.commit()
        flash('Patient ajouté avec succès!', 'success')
        return redirect(url_for('nouveau_rendez_vous'))
    except Exception as e:
        flash('Erreur lors de l\'ajout du patient.', 'error')
        return redirect(url_for('nouveau_patient'))

@app.route('/nouveau-rendez-vous')
def nouveau_rendez_vous():
    patients = Patient.query.all()
    kinesitherapeutes = Kinesitherapeute.query.all()
    return render_template('nouveau_rendez_vous.html', patients=patients, kinesitherapeutes=kinesitherapeutes)

@app.route('/ajouter-rendez-vous', methods=['POST'])
def ajouter_rendez_vous():
    try:
        rendez_vous = RendezVous(
            patient_id=request.form['patient_id'],
            kinesitherapeute_id=request.form['kinesitherapeute_id'],
            date_rdv=datetime.strptime(request.form['date_rdv'], '%Y-%m-%d').date(),
            heure_rdv=datetime.strptime(request.form['heure_rdv'], '%H:%M').time(),
            duree=int(request.form.get('duree', 60)),
            motif=request.form.get('motif', '')
        )
        db.session.add(rendez_vous)
        db.session.commit()
        flash('Rendez-vous ajouté avec succès!', 'success')
        return redirect(url_for('liste_rendez_vous'))
    except Exception as e:
        flash('Erreur lors de l\'ajout du rendez-vous.', 'error')
        return redirect(url_for('nouveau_rendez_vous'))

@app.route('/rendez-vous')
def liste_rendez_vous():
    rendez_vous = RendezVous.query.order_by(RendezVous.date_rdv, RendezVous.heure_rdv).all()
    return render_template('liste_rendez_vous.html', rendez_vous=rendez_vous)

@app.route('/patients')
def liste_patients():
    patients = Patient.query.order_by(Patient.nom, Patient.prenom).all()
    return render_template('liste_patients.html', patients=patients)

@app.route('/imprimer-rendez-vous/<int:rdv_id>')
def imprimer_rendez_vous(rdv_id):
    rendez_vous = RendezVous.query.get_or_404(rdv_id)
    return render_template('imprimer_rendez_vous.html', rendez_vous=rendez_vous)

@app.route('/recu/<int:rdv_id>')
def recu_a4(rdv_id):
    rendez_vous = RendezVous.query.get_or_404(rdv_id)
    # Numéro de reçu unique basé sur l'ID et la date
    numero_recu = f"REC-{rendez_vous.date_rdv.strftime('%Y%m%d')}-{rdv_id:04d}"
    return render_template('recu_a4.html', rendez_vous=rendez_vous, numero_recu=numero_recu)

@app.route('/imprimer-planning')
def imprimer_planning():
    date_debut = request.args.get('date_debut')
    date_fin = request.args.get('date_fin')
    kine_id = request.args.get('kine_id')

    query = RendezVous.query

    if date_debut:
        from datetime import datetime
        date_debut_obj = datetime.strptime(date_debut, '%Y-%m-%d').date()
        query = query.filter(RendezVous.date_rdv >= date_debut_obj)

    if date_fin:
        from datetime import datetime
        date_fin_obj = datetime.strptime(date_fin, '%Y-%m-%d').date()
        query = query.filter(RendezVous.date_rdv <= date_fin_obj)

    if kine_id:
        query = query.filter(RendezVous.kinesitherapeute_id == kine_id)

    rendez_vous = query.order_by(RendezVous.date_rdv, RendezVous.heure_rdv).all()
    kinesitherapeutes = Kinesitherapeute.query.all()

    return render_template('imprimer_planning.html',
                         rendez_vous=rendez_vous,
                         kinesitherapeutes=kinesitherapeutes,
                         date_debut=date_debut,
                         date_fin=date_fin,
                         kine_id=kine_id)

@app.route('/api/creneaux-disponibles')
def creneaux_disponibles():
    date_str = request.args.get('date')
    kine_id = request.args.get('kine_id')
    
    if not date_str or not kine_id:
        return jsonify([])
    
    date_rdv = datetime.strptime(date_str, '%Y-%m-%d').date()
    
    # Récupérer les rendez-vous existants pour ce kinésithérapeute à cette date
    rdv_existants = RendezVous.query.filter_by(
        kinesitherapeute_id=kine_id,
        date_rdv=date_rdv
    ).all()
    
    # Créer une liste des heures occupées
    heures_occupees = [rdv.heure_rdv.strftime('%H:%M') for rdv in rdv_existants]
    
    # Générer les créneaux disponibles (8h-18h par exemple)
    creneaux = []
    for heure in range(8, 18):
        for minute in [0, 30]:
            creneau = f"{heure:02d}:{minute:02d}"
            if creneau not in heures_occupees:
                creneaux.append(creneau)
    
    return jsonify(creneaux)

@app.route('/demo-data')
def demo_data():
    """Route pour créer des données de démonstration"""
    try:
        # Créer des patients de démonstration
        if Patient.query.count() == 0:
            from datetime import date, timedelta
            patients_demo = [
                Patient(nom='Martin', prenom='Sophie', telephone='01 23 45 67 89',
                       email='<EMAIL>', date_naissance=date(1985, 3, 15),
                       adresse='123 Rue de la Paix, 75001 Paris'),
                Patient(nom='Dubois', prenom='Pierre', telephone='01 98 76 54 32',
                       email='<EMAIL>', date_naissance=date(1978, 7, 22),
                       adresse='456 Avenue des Champs, 75008 Paris'),
                Patient(nom='Leroy', prenom='Marie', telephone='01 11 22 33 44',
                       email='<EMAIL>', date_naissance=date(1992, 11, 8),
                       adresse='789 Boulevard Saint-Germain, 75006 Paris')
            ]
            for patient in patients_demo:
                db.session.add(patient)
            db.session.commit()

            # Créer des rendez-vous de démonstration
            from datetime import datetime, time
            today = date.today()
            rdv_demo = [
                RendezVous(patient_id=1, kinesitherapeute_id=1,
                          date_rdv=today, heure_rdv=time(9, 0),
                          duree=60, motif='Rééducation genou', statut='confirmé'),
                RendezVous(patient_id=2, kinesitherapeute_id=2,
                          date_rdv=today, heure_rdv=time(14, 30),
                          duree=45, motif='Mal de dos chronique', statut='confirmé'),
                RendezVous(patient_id=3, kinesitherapeute_id=1,
                          date_rdv=today + timedelta(days=1), heure_rdv=time(10, 0),
                          duree=60, motif='Rééducation épaule', statut='confirmé')
            ]
            for rdv in rdv_demo:
                db.session.add(rdv)
            db.session.commit()

            flash('Données de démonstration créées avec succès!', 'success')
        else:
            flash('Des données existent déjà dans le système.', 'info')

    except Exception as e:
        flash('Erreur lors de la création des données de démonstration.', 'error')

    return redirect(url_for('index'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Ajouter des kinésithérapeutes par défaut s'ils n'existent pas
        if Kinesitherapeute.query.count() == 0:
            kines = [
                Kinesitherapeute(nom='Martin', prenom='Jean', specialite='Kinésithérapie générale'),
                Kinesitherapeute(nom='Dubois', prenom='Marie', specialite='Kinésithérapie sportive'),
                Kinesitherapeute(nom='Leroy', prenom='Pierre', specialite='Rééducation orthopédique')
            ]
            for kine in kines:
                db.session.add(kine)
            db.session.commit()

    app.run(debug=True, host='127.0.0.1', port=5001)
