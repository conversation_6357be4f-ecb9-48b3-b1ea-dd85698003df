from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date, time
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'votre-cle-secrete-ici'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///kinesitherapie.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# Modèles de base de données
class Patient(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    telephone = db.Column(db.String(20), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    adresse = db.Column(db.Text)
    rendez_vous = db.relationship('RendezVous', backref='patient', lazy=True)

    def __repr__(self):
        return f'<Patient {self.prenom} {self.nom}>'

class Kinesitherapeute(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    specialite = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    rendez_vous = db.relationship('RendezVous', backref='kinesitherapeute', lazy=True)

    def __repr__(self):
        return f'<Kinesitherapeute {self.prenom} {self.nom}>'

class RendezVous(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.Integer, db.ForeignKey('patient.id'), nullable=False)
    kinesitherapeute_id = db.Column(db.Integer, db.ForeignKey('kinesitherapeute.id'), nullable=False)
    date_rdv = db.Column(db.Date, nullable=False)
    heure_rdv = db.Column(db.Time, nullable=False)
    duree = db.Column(db.Integer, default=60)  # durée en minutes
    motif = db.Column(db.Text)
    statut = db.Column(db.String(20), default='confirmé')  # confirmé, annulé, terminé
    notes = db.Column(db.Text)
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<RendezVous {self.date_rdv} {self.heure_rdv}>'

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/nouveau-patient')
def nouveau_patient():
    return render_template('nouveau_patient.html')

@app.route('/ajouter-patient', methods=['POST'])
def ajouter_patient():
    try:
        patient = Patient(
            nom=request.form['nom'],
            prenom=request.form['prenom'],
            telephone=request.form['telephone'],
            email=request.form['email'],
            date_naissance=datetime.strptime(request.form['date_naissance'], '%Y-%m-%d').date(),
            adresse=request.form.get('adresse', '')
        )
        db.session.add(patient)
        db.session.commit()
        flash('Patient ajouté avec succès!', 'success')
        return redirect(url_for('nouveau_rendez_vous'))
    except Exception as e:
        flash('Erreur lors de l\'ajout du patient.', 'error')
        return redirect(url_for('nouveau_patient'))

@app.route('/nouveau-rendez-vous')
def nouveau_rendez_vous():
    patients = Patient.query.all()
    kinesitherapeutes = Kinesitherapeute.query.all()
    return render_template('nouveau_rendez_vous.html', patients=patients, kinesitherapeutes=kinesitherapeutes)

@app.route('/ajouter-rendez-vous', methods=['POST'])
def ajouter_rendez_vous():
    try:
        rendez_vous = RendezVous(
            patient_id=request.form['patient_id'],
            kinesitherapeute_id=request.form['kinesitherapeute_id'],
            date_rdv=datetime.strptime(request.form['date_rdv'], '%Y-%m-%d').date(),
            heure_rdv=datetime.strptime(request.form['heure_rdv'], '%H:%M').time(),
            duree=int(request.form.get('duree', 60)),
            motif=request.form.get('motif', '')
        )
        db.session.add(rendez_vous)
        db.session.commit()
        flash('Rendez-vous ajouté avec succès!', 'success')
        return redirect(url_for('liste_rendez_vous'))
    except Exception as e:
        flash('Erreur lors de l\'ajout du rendez-vous.', 'error')
        return redirect(url_for('nouveau_rendez_vous'))

@app.route('/rendez-vous')
def liste_rendez_vous():
    rendez_vous = RendezVous.query.order_by(RendezVous.date_rdv, RendezVous.heure_rdv).all()
    return render_template('liste_rendez_vous.html', rendez_vous=rendez_vous)

@app.route('/api/creneaux-disponibles')
def creneaux_disponibles():
    date_str = request.args.get('date')
    kine_id = request.args.get('kine_id')
    
    if not date_str or not kine_id:
        return jsonify([])
    
    date_rdv = datetime.strptime(date_str, '%Y-%m-%d').date()
    
    # Récupérer les rendez-vous existants pour ce kinésithérapeute à cette date
    rdv_existants = RendezVous.query.filter_by(
        kinesitherapeute_id=kine_id,
        date_rdv=date_rdv
    ).all()
    
    # Créer une liste des heures occupées
    heures_occupees = [rdv.heure_rdv.strftime('%H:%M') for rdv in rdv_existants]
    
    # Générer les créneaux disponibles (8h-18h par exemple)
    creneaux = []
    for heure in range(8, 18):
        for minute in [0, 30]:
            creneau = f"{heure:02d}:{minute:02d}"
            if creneau not in heures_occupees:
                creneaux.append(creneau)
    
    return jsonify(creneaux)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # Ajouter des kinésithérapeutes par défaut s'ils n'existent pas
        if Kinesitherapeute.query.count() == 0:
            kines = [
                Kinesitherapeute(nom='Martin', prenom='Jean', specialite='Kinésithérapie générale'),
                Kinesitherapeute(nom='Dubois', prenom='Marie', specialite='Kinésithérapie sportive'),
                Kinesitherapeute(nom='Leroy', prenom='Pierre', specialite='Rééducation orthopédique')
            ]
            for kine in kines:
                db.session.add(kine)
            db.session.commit()
    
    app.run(debug=True)
