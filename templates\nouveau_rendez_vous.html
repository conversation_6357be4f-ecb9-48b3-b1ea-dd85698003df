{% extends "base.html" %}

{% block title %}Nouveau Rendez-vous{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-calendar-plus"></i> Nouveau Rendez-vous
                </h4>
            </div>
            <div class="card-body">
                <form action="{{ url_for('ajouter_rendez_vous') }}" method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="patient_id" class="form-label">Patient *</label>
                            <select class="form-select" id="patient_id" name="patient_id" required>
                                <option value="">Sélectionner un patient</option>
                                {% for patient in patients %}
                                <option value="{{ patient.id }}">{{ patient.prenom }} {{ patient.nom }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="kinesitherapeute_id" class="form-label">Kinésithérapeute *</label>
                            <select class="form-select" id="kinesitherapeute_id" name="kinesitherapeute_id" required>
                                <option value="">Sélectionner un kinésithérapeute</option>
                                {% for kine in kinesitherapeutes %}
                                <option value="{{ kine.id }}">{{ kine.prenom }} {{ kine.nom }} - {{ kine.specialite }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_rdv" class="form-label">Date *</label>
                            <input type="date" class="form-control" id="date_rdv" name="date_rdv" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="heure_rdv" class="form-label">Heure *</label>
                            <input type="time" class="form-control" id="heure_rdv" name="heure_rdv" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="duree" class="form-label">Durée (minutes)</label>
                            <select class="form-select" id="duree" name="duree">
                                <option value="30">30 minutes</option>
                                <option value="60" selected>60 minutes</option>
                                <option value="90">90 minutes</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="motif" class="form-label">Motif de consultation</label>
                        <textarea class="form-control" id="motif" name="motif" rows="3"></textarea>
                    </div>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">Retour</a>
                        <button type="submit" class="btn btn-primary">Planifier le rendez-vous</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
