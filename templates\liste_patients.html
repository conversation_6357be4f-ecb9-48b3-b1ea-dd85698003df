{% extends "base.html" %}

{% block title %}Liste des Patients{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="bi bi-people"></i> Liste des Patients
                </h4>
                <a href="{{ url_for('nouveau_patient') }}" class="btn btn-primary">
                    <i class="bi bi-person-plus"></i> Nouveau Patient
                </a>
            </div>
            <div class="card-body">
                {% if patients %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Nom</th>
                                <th>Prénom</th>
                                <th>Téléphone</th>
                                <th>Email</th>
                                <th>Date de naissance</th>
                                <th>Âge</th>
                                <th>Adresse</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for patient in patients %}
                            <tr>
                                <td><strong>{{ patient.nom }}</strong></td>
                                <td>{{ patient.prenom }}</td>
                                <td>
                                    <a href="tel:{{ patient.telephone }}" class="text-decoration-none">
                                        <i class="bi bi-telephone"></i> {{ patient.telephone }}
                                    </a>
                                </td>
                                <td>
                                    <a href="mailto:{{ patient.email }}" class="text-decoration-none">
                                        <i class="bi bi-envelope"></i> {{ patient.email }}
                                    </a>
                                </td>
                                <td>{{ patient.date_naissance.strftime('%d/%m/%Y') }}</td>
                                <td>
                                    <span data-birth-date="{{ patient.date_naissance.strftime('%Y-%m-%d') }}">
                                        Calcul...
                                    </span>
                                </td>
                                <td>
                                    {% if patient.adresse %}
                                        {{ patient.adresse[:50] }}{% if patient.adresse|length > 50 %}...{% endif %}
                                    {% else %}
                                        <span class="text-muted">Non renseignée</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('nouveau_rendez_vous') }}?patient_id={{ patient.id }}" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="Prendre rendez-vous">
                                            <i class="bi bi-calendar-plus"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-info" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#patientModal{{ patient.id }}"
                                                title="Voir détails">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <!-- Modal pour les détails du patient -->
                            <div class="modal fade" id="patientModal{{ patient.id }}" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">
                                                <i class="bi bi-person"></i> {{ patient.prenom }} {{ patient.nom }}
                                            </h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <strong>Nom complet:</strong><br>
                                                    {{ patient.prenom }} {{ patient.nom }}
                                                </div>
                                                <div class="col-md-6">
                                                    <strong>Date de naissance:</strong><br>
                                                    {{ patient.date_naissance.strftime('%d/%m/%Y') }}
                                                </div>
                                            </div>
                                            <hr>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <strong>Téléphone:</strong><br>
                                                    <a href="tel:{{ patient.telephone }}">{{ patient.telephone }}</a>
                                                </div>
                                                <div class="col-md-6">
                                                    <strong>Email:</strong><br>
                                                    <a href="mailto:{{ patient.email }}">{{ patient.email }}</a>
                                                </div>
                                            </div>
                                            {% if patient.adresse %}
                                            <hr>
                                            <div class="row">
                                                <div class="col-12">
                                                    <strong>Adresse:</strong><br>
                                                    {{ patient.adresse }}
                                                </div>
                                            </div>
                                            {% endif %}
                                            <hr>
                                            <div class="row">
                                                <div class="col-12">
                                                    <strong>Rendez-vous:</strong><br>
                                                    {% set rdv_count = patient.rendez_vous|length %}
                                                    {% if rdv_count > 0 %}
                                                        {{ rdv_count }} rendez-vous programmé(s)
                                                    {% else %}
                                                        Aucun rendez-vous programmé
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                            <a href="{{ url_for('nouveau_rendez_vous') }}?patient_id={{ patient.id }}" 
                                               class="btn btn-primary">
                                                <i class="bi bi-calendar-plus"></i> Prendre RDV
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Statistiques -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> 
                            <strong>{{ patients|length }}</strong> patient(s) enregistré(s) dans le système.
                        </div>
                    </div>
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-person-x fs-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">Aucun patient enregistré</h5>
                    <p class="text-muted">Commencez par ajouter votre premier patient.</p>
                    <a href="{{ url_for('nouveau_patient') }}" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i> Premier patient
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Calculer l'âge côté client pour plus de précision
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    
    document.querySelectorAll('[data-birth-date]').forEach(function(element) {
        const birthDate = new Date(element.getAttribute('data-birth-date'));
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        
        element.textContent = age + ' ans';
    });
});
</script>
{% endblock %}
