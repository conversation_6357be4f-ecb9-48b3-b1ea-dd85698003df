{% extends "base.html" %}

{% block title %}Nouveau Patient - Système de Rendez-vous Kinésithérapie{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-person-plus"></i> Nouveau Patient
                </h4>
            </div>
            <div class="card-body">
                <form action="{{ url_for('ajouter_patient') }}" method="POST" id="patientForm">
                    <div class="row">
                        <!-- Nom -->
                        <div class="col-md-6 mb-3">
                            <label for="nom" class="form-label">
                                <i class="bi bi-person"></i> Nom *
                            </label>
                            <input type="text" class="form-control" id="nom" name="nom" required>
                            <div class="invalid-feedback">
                                Veuillez saisir le nom du patient.
                            </div>
                        </div>

                        <!-- Prénom -->
                        <div class="col-md-6 mb-3">
                            <label for="prenom" class="form-label">
                                <i class="bi bi-person"></i> Prénom *
                            </label>
                            <input type="text" class="form-control" id="prenom" name="prenom" required>
                            <div class="invalid-feedback">
                                Veuillez saisir le prénom du patient.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Téléphone -->
                        <div class="col-md-6 mb-3">
                            <label for="telephone" class="form-label">
                                <i class="bi bi-telephone"></i> Téléphone *
                            </label>
                            <input type="tel" class="form-control" id="telephone" name="telephone" 
                                   placeholder="01 23 45 67 89" required>
                            <div class="invalid-feedback">
                                Veuillez saisir un numéro de téléphone valide.
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="bi bi-envelope"></i> Email *
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="<EMAIL>" required>
                            <div class="invalid-feedback">
                                Veuillez saisir une adresse email valide.
                            </div>
                        </div>
                    </div>

                    <!-- Date de naissance -->
                    <div class="mb-3">
                        <label for="date_naissance" class="form-label">
                            <i class="bi bi-calendar"></i> Date de naissance *
                        </label>
                        <input type="date" class="form-control" id="date_naissance" name="date_naissance" required>
                        <div class="invalid-feedback">
                            Veuillez saisir la date de naissance.
                        </div>
                    </div>

                    <!-- Adresse -->
                    <div class="mb-3">
                        <label for="adresse" class="form-label">
                            <i class="bi bi-geo-alt"></i> Adresse
                        </label>
                        <textarea class="form-control" id="adresse" name="adresse" rows="3" 
                                  placeholder="Adresse complète du patient"></textarea>
                    </div>

                    <!-- Boutons -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">
                            <i class="bi bi-arrow-left"></i> Retour
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Enregistrer le patient
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Validation côté client
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Formatage du numéro de téléphone
document.getElementById('telephone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    let formattedValue = value.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5');
    if (formattedValue.length > 14) {
        formattedValue = formattedValue.substring(0, 14);
    }
    e.target.value = formattedValue;
});
</script>
{% endblock %}
