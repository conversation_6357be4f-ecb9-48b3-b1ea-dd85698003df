#!/usr/bin/env python3
import os
import sys

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app
    print("Application Flask importée avec succès")
    
    # C<PERSON>er les tables de base de données
    with app.app_context():
        from app import db, Kinesitherapeute
        db.create_all()
        print("Base de données initialisée")
        
        # Ajouter des kinésithérapeutes par défaut s'ils n'existent pas
        if Kinesitherapeute.query.count() == 0:
            kines = [
                Kinesitherapeute(nom='<PERSON>', prenom='<PERSON>', specialite='Kinésithérapie générale'),
                Kinesithera<PERSON><PERSON>(nom='Dubois', prenom='Marie', specialite='Kinésithérapie sportive'),
                Kinesitherapeute(nom='Leroy', prenom='Pierre', specialite='Rééducation orthopédique')
            ]
            for kine in kines:
                db.session.add(kine)
            db.session.commit()
            print("Kinésithérapeutes par défaut ajoutés")
    
    print("Démarrage du serveur Flask sur http://127.0.0.1:5001")
    app.run(host='127.0.0.1', port=5001, debug=True)
    
except Exception as e:
    print(f"Erreur lors du démarrage de l'application: {e}")
    import traceback
    traceback.print_exc()
