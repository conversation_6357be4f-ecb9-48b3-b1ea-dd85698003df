<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reçu - {{ numero_recu }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        
        @media print {
            .no-print { display: none !important; }
            body { 
                font-size: 11pt; 
                line-height: 1.4;
                color: #000;
            }
            .card { 
                border: 1px solid #000 !important; 
                box-shadow: none !important;
            }
            .table { 
                border: 1px solid #000 !important; 
            }
            .table th, .table td { 
                border: 1px solid #000 !important; 
                padding: 8px !important;
            }
            .bg-primary { 
                background-color: #f8f9fa !important; 
                color: #000 !important; 
            }
            .text-primary { 
                color: #000 !important; 
            }
        }
        
        @media screen {
            body { 
                background-color: #f8f9fa; 
                padding: 20px; 
            }
        }
        
        .header-section {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .cabinet-info {
            text-align: left;
        }
        
        .recu-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin: 20px 0;
        }
        
        .numero-recu {
            text-align: right;
            font-weight: bold;
            color: #6c757d;
        }
        
        .section-title {
            background-color: #007bff;
            color: white;
            padding: 8px 15px;
            margin: 20px 0 10px 0;
            font-weight: bold;
        }
        
        .montant-section {
            background-color: #f8f9fa;
            border: 2px solid #007bff;
            padding: 20px;
            margin: 20px 0;
        }
        
        .total-amount {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        
        .signature-box {
            border: 1px solid #dee2e6;
            height: 80px;
            margin-top: 10px;
        }
        
        .footer-info {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 10pt;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Boutons d'action (masqués à l'impression) -->
        <div class="no-print mb-3">
            <div class="d-flex justify-content-between">
                <a href="{{ url_for('liste_rendez_vous') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Retour à la liste
                </a>
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="bi bi-printer"></i> Imprimer le reçu
                </button>
            </div>
        </div>

        <!-- Contenu du reçu A4 -->
        <div class="card">
            <div class="card-body">
                <!-- En-tête du cabinet -->
                <div class="header-section">
                    <div class="row">
                        <div class="col-md-8 cabinet-info">
                            <h3 class="text-primary mb-1">Cabinet de Kinésithérapie</h3>
                            <p class="mb-1"><strong>Dr. {{ rendez_vous.kinesitherapeute.prenom }} {{ rendez_vous.kinesitherapeute.nom }}</strong></p>
                            <p class="mb-1">Masseur-Kinésithérapeute D.E.</p>
                            <p class="mb-1">{{ rendez_vous.kinesitherapeute.specialite }}</p>
                            <hr class="my-2">
                            <p class="mb-1"><i class="bi bi-geo-alt"></i> 123 Avenue de la Santé, 75014 Paris</p>
                            <p class="mb-1"><i class="bi bi-telephone"></i> {{ rendez_vous.kinesitherapeute.telephone or '01 23 45 67 89' }}</p>
                            <p class="mb-1"><i class="bi bi-envelope"></i> {{ rendez_vous.kinesitherapeute.email or '<EMAIL>' }}</p>
                            <p class="mb-0"><strong>SIRET :</strong> 123 456 789 00012</p>
                        </div>
                        <div class="col-md-4">
                            <div class="numero-recu">
                                <p class="mb-1">Reçu N°</p>
                                <h4 class="text-primary">{{ numero_recu }}</h4>
                                <p class="mb-0">Date : {{ rendez_vous.date_rdv.strftime('%d/%m/%Y') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Titre du reçu -->
                <div class="recu-title">
                    REÇU DE SOINS DE KINÉSITHÉRAPIE
                </div>

                <!-- Informations patient -->
                <div class="section-title">
                    <i class="bi bi-person"></i> INFORMATIONS PATIENT
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>Nom :</strong> {{ rendez_vous.patient.nom }}</p>
                        <p><strong>Prénom :</strong> {{ rendez_vous.patient.prenom }}</p>
                        <p><strong>Date de naissance :</strong> {{ rendez_vous.patient.date_naissance.strftime('%d/%m/%Y') }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Téléphone :</strong> {{ rendez_vous.patient.telephone }}</p>
                        <p><strong>Email :</strong> {{ rendez_vous.patient.email }}</p>
                        {% if rendez_vous.patient.adresse %}
                        <p><strong>Adresse :</strong> {{ rendez_vous.patient.adresse }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Détails de la séance -->
                <div class="section-title">
                    <i class="bi bi-calendar-check"></i> DÉTAILS DE LA SÉANCE
                </div>
                <table class="table table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Date de la séance</th>
                            <th>Heure</th>
                            <th>Durée</th>
                            <th>Type de soin</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ rendez_vous.date_rdv.strftime('%d/%m/%Y') }}</td>
                            <td>{{ rendez_vous.heure_rdv.strftime('%H:%M') }}</td>
                            <td>{{ rendez_vous.duree }} minutes</td>
                            <td>{{ rendez_vous.motif or 'Séance de kinésithérapie' }}</td>
                        </tr>
                    </tbody>
                </table>

                <!-- Prestations et tarifs -->
                <div class="section-title">
                    <i class="bi bi-receipt"></i> PRESTATIONS ET TARIFS
                </div>
                <table class="table table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Désignation</th>
                            <th>Quantité</th>
                            <th>Prix unitaire</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Séance de kinésithérapie ({{ rendez_vous.duree }}min)</td>
                            <td>1</td>
                            <td>45,00 €</td>
                            <td>45,00 €</td>
                        </tr>
                        {% if rendez_vous.duree > 60 %}
                        <tr>
                            <td>Supplément séance longue</td>
                            <td>1</td>
                            <td>15,00 €</td>
                            <td>15,00 €</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>

                <!-- Montant total -->
                <div class="montant-section">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>MONTANT TOTAL À PAYER</h5>
                            <p class="mb-0">Mode de paiement : Espèces / Chèque / Carte bancaire</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="total-amount">
                                {% if rendez_vous.duree > 60 %}
                                60,00 €
                                {% else %}
                                45,00 €
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes et observations -->
                {% if rendez_vous.notes %}
                <div class="section-title">
                    <i class="bi bi-sticky"></i> NOTES ET OBSERVATIONS
                </div>
                <div class="mb-3">
                    <p>{{ rendez_vous.notes }}</p>
                </div>
                {% endif %}

                <!-- Signatures -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h6>Signature du patient :</h6>
                        <p class="mb-1">Je certifie avoir reçu les soins mentionnés ci-dessus</p>
                        <div class="signature-box"></div>
                    </div>
                    <div class="col-md-6">
                        <h6>Signature du praticien :</h6>
                        <p class="mb-1">{{ rendez_vous.kinesitherapeute.prenom }} {{ rendez_vous.kinesitherapeute.nom }}</p>
                        <div class="signature-box"></div>
                    </div>
                </div>

                <!-- Informations légales -->
                <div class="footer-info">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>Informations légales :</strong></p>
                            <p class="mb-1">• Ce reçu peut être remboursé par votre mutuelle</p>
                            <p class="mb-1">• Conservez ce document pour vos déclarations</p>
                            <p class="mb-0">• Délai de réclamation : 30 jours</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <p class="mb-1">Document généré le : <span id="generation-date"></span></p>
                            <p class="mb-0">Cabinet de Kinésithérapie - Tous droits réservés</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Afficher la date de génération
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('fr-FR') + ' à ' + now.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
            document.getElementById('generation-date').textContent = dateStr;
        });
    </script>
</body>
</html>
